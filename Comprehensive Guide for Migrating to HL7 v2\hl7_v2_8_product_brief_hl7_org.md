# HL7 Messaging Standard Version 2.8

## Section 1d: Version 2 (V2)  
Section 2: Clinical and Administrative Domains  

# HL7 Messaging Standard Version 2.8

### DESCRIPTION

HL7’s Version 2.x (V2) messaging standard is the workhorse of electronic data exchange in the clinical domain and arguably the most widely implemented standard for healthcare in the world. This messaging standard allows the exchange of clinical data between systems. It is designed to support a central patient care system as well as a more distributed environment where data resides in departmental systems.

### ALTERNATIVE NAMES

HL7 Messaging Standard Version 2.8 may also go by the following names or acronyms:

Version 2.8, V2, V2.8, V28

### TARGETS

*   Health Care IT
*   Healthcare Providers

### BENEFITS

*   Supports the majority of the common interfaces used in the healthcare industry globally
*   Provides a framework for negotiations of what is not in the standard
*   Reduces implementation costs
*   Generally backward compatible

### IMPLEMENTATIONS/CASE STUDIES

*   95% of US healthcare organizations use HL7 V2.x
*   More than 35 countries have HL7 V2.x implementations

### DEVELOPMENT BACKGROUND

As one of the most widely implemented standards for healthcare information in the world, the Version 2 Messaging Standard was first released in October 1987 as an Application Protocol for Electronic Data Exchange in Healthcare Environments. Version 2.8, representing the latest update to the Version 2 Standard, was published in 2014.

Due to its widespread use, Version 2 will continue to play an integral part in healthcare messaging, even with the HL7 Version 3 Normative Edition. HL7 is committed to supporting and extending Version 2 in parallel with Version 3, providing continuity for current installations.

### RELATED DOCUMENTS

HL7 Messaging Standard Version 2.8 | 

[(Download)]() (29.07 MB)

 |

### BALLOT TYPE

*   Normative

### STATUS DATE

2014-02-21

### RESPONSIBLE WORK GROUPS

### PRODUCT TYPE

*   ANSI-approved

### FAMILY

*   V2

### CURRENT STATE

*   Retired

### REALM

*   Universal

