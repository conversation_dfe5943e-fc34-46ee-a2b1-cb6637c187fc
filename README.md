# HL7 Migration Guide

A comprehensive guide for HL7 migration built with Next.js and TypeScript.

## Getting Started

First, install the dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

Then, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Features

- **TypeScript**: Strongly typed codebase for better developer experience
- **Next.js App Router**: Modern routing system with server components
- **Tailwind CSS**: Utility-first CSS framework for rapid UI development
- **ESLint**: Code linting for identifying and fixing problems

## Project Structure

- `src/app`: Contains the main application code
  - `page.tsx`: The main page component
  - `layout.tsx`: The root layout component
  - `globals.css`: Global styles with Tailwind directives

## Learn More

To learn more about the technologies used in this project, check out the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [TypeScript Documentation](https://www.typescriptlang.org/docs/) - learn about TypeScript
- [Tailwind CSS Documentation](https://tailwindcss.com/docs) - learn about Tailwind CSS
- [HL7 Standards](https://www.hl7.org/) - learn about HL7 standards